# Nepali App    - Complete System Documentation

 
A comprehensive documentation for the Nepali App microservices architecture, providing detailed system information, API specifications, deployment guides, and integration examples for developers and system administrators.


## 🏗️ System Architecture Overview

The Nepali App follows a **simplified 3-service microservices architecture** designed for optimal performance, maintainability, and scalability:

### Core Infrastructure
- **Traefik** - Reverse proxy and load balancer (External Port: 8204 for API, 8205 for Dashboard)
- **Redis** - Session management, caching, and Socket.IO communication
- **MongoDB** - Primary database for data persistence (External, not containerized)
- **Docker** - Containerization and orchestration
- **MinIO** - Object storage for media files

### Microservices Architecture

| Service | Base URL | Port | Description | Key Features |
|---------|----------|------|-------------|--------------|
| **Auth Service** | `http://localhost:8204/v1/auth` | 8001 | Authentication & User Management | JWT tokens, role-based access, OAuth integration |
| **Socket Service** | `http://localhost:8204/v1/socket` | 8002 | Real-time Communication | Socket.IO, audio streaming, session management |
| **Management Service** | `http://localhost:8204/v1/management` | 8003 | CRUD Operations | Task sets, submissions, scoring, media handling |

### External Access Points
- **Production API**: `http://napp-api.nextai.asia:8204`
- **Local Development**: `http://localhost:8204`
- **Traefik Dashboard**: `http://localhost:8205`
- **Interactive API Docs**: `http://localhost:8204/docs`

### Service Responsibilities

#### Auth Service (`/v1/auth`)
- User authentication (password & Google OAuth)
- JWT token generation and validation
- Role-based access control (Admin, User, Agent)
- User registration and invitation system
- Password management and security

#### Socket Service (`/v1/socket`)
- Real-time Socket.IO communication
- Audio streaming and chunk processing
- Session-based authentication
- WebSocket connection lifecycle management
- Real-time task generation coordination

#### Management Service (`/v1/management`)
- Task sets and task items CRUD operations
- Submission handling and scoring
- Media file management (upload/download)
- User statistics and leaderboards
- Database operations and data persistence

## 🔐 Authentication & Authorization System

### Authentication Architecture

The system implements a **JWT-based authentication** with role-based access control (RBAC) across all microservices:

#### Authentication Methods
1. **Password Authentication** - Traditional username/password login
2. **Google OAuth 2.0** - Social login integration
3. **Invitation-based Registration** - Admin-controlled user onboarding

#### JWT Token Management
- **Token Expiration**: 2 hours (7200 seconds)
- **Algorithm**: HS256
- **Validation**: Automatic middleware validation on protected endpoints
- **Refresh**: Manual re-authentication required

### Complete Authentication Flow

```mermaid
graph TD
    A[Client Application] -->|1. POST /v1/auth/login| B[Auth Service]
    B -->|2. Validate Credentials| C[MongoDB Users Collection]
    C -->|3. User Found| B
    B -->|4. Generate JWT| D[JWT Token]
    D -->|5. Return Token| A
    A -->|6. Include in Headers| E[Protected Endpoints]
    E -->|7. Validate Token| F[JWT Middleware]
    F -->|8. Extract User Info| G[Request Processing]
```

### JWT Token Usage

**Header Format**:
```http
Authorization: Bearer <your_jwt_token>
```

**Token Payload Structure**:
```json
{
  "sub": "username",
  "user_id": "user_object_id",
  "tenant_id": "tenant_object_id",
  "role": "user|admin|agent",
  "exp": 1640995200,
  "iat": 1640988000
}
```

**Example Request**:
```bash
curl -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..." \
     http://localhost:8204/v1/management/task-sets/filtered
```

### Role-Based Access Control (RBAC)

| Role | Permissions | Access Level |
|------|-------------|--------------|
| **Admin** | Full system access, user management, role management | All endpoints |
| **User** | Standard user operations, task management, submissions | User-specific endpoints |
| **Agent** | Service-specific permissions, limited admin functions | Agent-specific endpoints |

### Authentication Endpoints

#### User Registration
```http
POST /v1/auth/signup
Content-Type: application/json

{
  "username": "john_doe",
  "email": "<EMAIL>",
  "password": "securePassword123",
  "client_id": "nepali_app",
  "full_name": "John Doe",
  "phone_number": "+**********",
  "country_code": "US"
}
```

**Response (200 OK)**:
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer",
  "expires_in": 7200,
  "user": {
    "id": "user_id_here",
    "username": "john_doe",
    "email": "<EMAIL>",
    "role": "user",
    "full_name": "John Doe",
    "auth_provider": "password",
    "onboarding_completed": false
  }
}
```

#### User Login
```http
POST /v1/auth/login
Content-Type: application/json

{
  "username": "john_doe",
  "password": "securePassword123",
  "client_id": "nepali_app"
}
```

**Response (200 OK)**: Same structure as registration

#### Google OAuth Authentication
```http
POST /v1/auth/google-auth
Content-Type: application/json

{
  "id_token": "google_id_token_here",
  "client_id": "nepali_app"
}
```

#### Token Verification
```http
GET /v1/auth/verify_token
Authorization: Bearer <your_jwt_token>
```

**Response (200 OK)**:
```json
{
  "valid": true,
  "user": {
    "id": "user_id_here",
    "username": "john_doe",
    "email": "<EMAIL>",
    "role": "user",
    "tenant_id": "tenant_id_here"
  }
}
```

#### User Onboarding
```http
POST /v1/auth/onboarding
Authorization: Bearer <your_jwt_token>
Content-Type: application/json

{
  "age": 7,
  "difficulty_level": 2,
  "preferred_topics": ["math", "science"]
}
```

### Error Responses

All authentication errors follow this format:

```json
{
  "detail": "Error message here",
  "status_code": 401
}
```

**Common Error Codes**:
- `401`: Invalid credentials or expired token
- `403`: Insufficient permissions
- `422`: Validation error (missing fields)

## 📡 Real-Time Audio Processing & Socket.IO System

The Socket Service implements a sophisticated real-time audio streaming system using Socket.IO for bidirectional communication, enabling live audio processing and task generation.

### Socket.IO Architecture

```mermaid
graph TD
    A[Frontend Client] -->|1. POST /v1/socket/connect| B[Socket Service]
    B -->|2. Session Token| A
    A -->|3. WebSocket Connect| C[Socket.IO Server]
    C -->|4. Authentication| D[Redis Session Store]
    D -->|5. Session Valid| C
    A -->|6. stream_starting| C
    C -->|7. stream_starting_ack| A
    A -->|8. binary_data chunks| C
    C -->|9. Audio Buffer| E[Audio Processing Engine]
    E -->|10. Gemini AI| F[Task Generation]
    F -->|11. Generated Tasks| G[MongoDB]
    C -->|12. task_generation_complete| A
```

### Complete Socket.IO Integration Flow

#### Step 1: Authentication & Session Creation
```bash
# Create authenticated Socket.IO session
curl -X POST "http://localhost:8204/v1/socket/connect" \
  -H "Authorization: Bearer <your_jwt_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "difficulty": "medium",
    "num_tasks": 4,
    "chunk_threshold": 20,
    "metadata": {
      "user_preference": "nepali_language"
    }
  }'
```

**Response (200 OK):**
```json
{
  "session_token": "session_abc123",
  "session_id": "sess_xyz789",
  "websocket_url": "/v1/socket/socket.io",
  "expires_at": "2024-01-01T12:00:00Z",
  "configuration": {
    "difficulty": "medium",
    "num_tasks": 4,
    "chunk_threshold": 20
  },
  "status": "ready",
  "instructions": {
    "next_step": "Connect to WebSocket using session_token",
    "websocket_endpoint": "/v1/socket/socket.io",
    "auth_method": "Include session_token in auth object",
    "flow": {
      "1": "Send 'stream_starting' event to begin",
      "2": "Wait for 'stream_starting_ack' response",
      "3": "Send binary audio chunks",
      "4": "Send 'stream_completed' or 'stream_stop' to finish"
    },
    "events": {
      "stream_starting": "Begin audio streaming session",
      "binary_data": "Send audio chunks for processing",
      "stream_completed": "Signal normal completion",
      "stream_stop": "Signal forced stop"
    }
  }
}
```

#### Step 2: WebSocket Connection
```javascript
// Frontend JavaScript example
import io from 'socket.io-client';

const socket = io('http://localhost:8204/v1/socket', {
  auth: {
    session_token: 'session_abc123'  // From Step 1 response
  },
  transports: ['websocket']
});

socket.on('connect', () => {
  console.log('Connected to Socket.IO server');
});
```

#### Step 3: Audio Streaming Events

### Socket.IO Event Specifications

#### Client → Server Events

##### `stream_starting`
**Description**: Initialize audio streaming session
**Payload:**
```json
{
  "session_id": "sess_xyz789",
  "audio_format": "pcm",
  "sample_rate": 16000,
  "channels": 1,
  "metadata": {
    "device_info": "microphone_model",
    "user_id": "user_123"
  }
}
```

**Frontend Example:**
```javascript
socket.emit('stream_starting', {
  session_id: 'sess_xyz789',
  audio_format: 'pcm',
  sample_rate: 16000,
  channels: 1,
  metadata: {
    device_info: 'Web Audio API',
    user_id: 'user_123'
  }
});
```

##### `binary_data`
**Description**: Send audio chunk for processing
**Payload:** Binary audio data with metadata
```javascript
// Frontend example - sending audio chunks
const audioChunk = new Uint8Array(audioBuffer);
socket.emit('binary_data', {
  session_id: 'sess_xyz789',
  chunk_index: 1,
  timestamp: Date.now(),
  data: audioChunk
});
```

##### `stream_completed`
**Description**: Signal normal completion of audio stream
**Payload:**
```json
{
  "session_id": "sess_xyz789",
  "total_chunks": 45,
  "duration_ms": 15000,
  "completion_reason": "user_finished"
}
```

##### `stream_stop`
**Description**: Force stop audio streaming
**Payload:**
```json
{
  "session_id": "sess_xyz789",
  "reason": "user_cancelled",
  "timestamp": "2024-01-01T12:05:00Z"
}
```

#### Server → Client Events

##### `stream_starting_ack`
**Description**: Confirmation that streaming session is ready
**Payload:**
```json
{
  "session_id": "sess_xyz789",
  "status": "ready",
  "message": "Audio streaming session initialized",
  "configuration": {
    "chunk_threshold": 20,
    "processing_enabled": true
  }
}
```

##### `task_generation_processing`
**Description**: Notification that task generation has started
**Payload:**
```json
{
  "session_id": "sess_xyz789",
  "status": "processing",
  "chunks_received": 20,
  "estimated_completion": "2024-01-01T12:06:00Z",
  "message": "Processing audio with Gemini AI..."
}
```

##### `task_generation_complete`
**Description**: Task generation completed with generated tasks
**Payload:**
```json
{
  "session_id": "sess_xyz789",
  "task_set_id": "taskset_456",
  "status": "completed",
  "tasks": [
    {
      "id": "task_001",
      "type": "single_choice",
      "question": "नेपालको राजधानी कुन हो?",
      "translated_question": "What is the capital of Nepal?",
      "options": {
        "a": "काठमाडौं",
        "b": "पोखरा",
        "c": "चितवन",
        "d": "भक्तपुर"
      },
      "answer": "a",
      "total_score": 10,
      "complexity": 1
    }
  ],
  "total_tasks": 4,
  "total_score": 40,
  "processing_time_ms": 3500
}
```

##### `task_generation_cancelled`
**Description**: Task generation was cancelled
**Payload:**
```json
{
  "session_id": "sess_xyz789",
  "status": "cancelled",
  "reason": "user_requested|timeout|error",
  "message": "Task generation cancelled by user"
}
```

##### `stream_error`
**Description**: Error occurred during streaming
**Payload:**
```json
{
  "session_id": "sess_xyz789",
  "error_code": "AUDIO_PROCESSING_ERROR",
  "message": "Failed to process audio chunk",
  "details": {
    "chunk_index": 15,
    "error_type": "format_invalid"
  },
  "recoverable": false
}
```

### Audio Processing Architecture
- **Minimum Buffer**: Requires 20 audio chunks before processing begins
- **Parallel Processing**: Processes 20-chunk batches while collecting new chunks
- **Chunk Management**: Each chunk indexed and tracked for reliability
- **Gemini AI Integration**: Real-time task generation from combined audio input
- **Session Management**: Redis-backed session state with automatic cleanup

### Complete Frontend Integration Example

```javascript
class NepaliAppAudioStreaming {
  constructor(baseUrl, authToken) {
    this.baseUrl = baseUrl;
    this.authToken = authToken;
    this.socket = null;
    this.sessionData = null;
    this.isRecording = false;
    this.audioChunks = [];
    this.chunkIndex = 0;
  }

  // Step 1: Create authenticated session
  async createSession(difficulty = 'medium', numTasks = 4) {
    const response = await fetch(`${this.baseUrl}/v1/socket/connect`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.authToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        difficulty,
        num_tasks: numTasks,
        chunk_threshold: 20
      })
    });

    this.sessionData = await response.json();
    return this.sessionData;
  }

  // Step 2: Connect to Socket.IO
  connectSocket() {
    this.socket = io(`${this.baseUrl}/v1/socket`, {
      auth: {
        session_token: this.sessionData.session_token
      },
      transports: ['websocket']
    });

    // Set up event listeners
    this.socket.on('connect', () => {
      console.log('Connected to Socket.IO server');
      this.startAudioStream();
    });

    this.socket.on('stream_starting_ack', (data) => {
      console.log('Stream ready:', data);
      this.isRecording = true;
    });

    this.socket.on('task_generation_processing', (data) => {
      console.log('Processing started:', data);
      // Update UI to show processing state
    });

    this.socket.on('task_generation_complete', (data) => {
      console.log('Tasks generated:', data);
      this.handleGeneratedTasks(data.tasks);
    });

    this.socket.on('stream_error', (error) => {
      console.error('Stream error:', error);
      this.handleError(error);
    });
  }

  // Step 3: Start audio streaming
  startAudioStream() {
    this.socket.emit('stream_starting', {
      session_id: this.sessionData.session_id,
      audio_format: 'pcm',
      sample_rate: 16000,
      channels: 1,
      metadata: {
        device_info: 'Web Audio API',
        user_id: 'current_user'
      }
    });
  }

  // Step 4: Send audio chunks
  sendAudioChunk(audioBuffer) {
    if (!this.isRecording) return;

    const audioChunk = new Uint8Array(audioBuffer);
    this.socket.emit('binary_data', {
      session_id: this.sessionData.session_id,
      chunk_index: this.chunkIndex++,
      timestamp: Date.now(),
      data: audioChunk
    });
  }

  // Step 5: Complete streaming
  completeStream() {
    this.isRecording = false;
    this.socket.emit('stream_completed', {
      session_id: this.sessionData.session_id,
      total_chunks: this.chunkIndex,
      duration_ms: Date.now() - this.startTime,
      completion_reason: 'user_finished'
    });
  }

  // Handle generated tasks
  handleGeneratedTasks(tasks) {
    tasks.forEach(task => {
      console.log(`Task: ${task.question}`);
      console.log(`Options:`, task.options);
      // Update UI with generated tasks
    });
  }

  // Error handling
  handleError(error) {
    console.error('Audio streaming error:', error);
    // Implement error recovery or user notification
  }

  // Cleanup
  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
    }
  }
}

// Usage example
const audioStreaming = new NepaliAppAudioStreaming(
  'http://localhost:8204',
  'your_jwt_token_here'
);

// Start the complete flow
audioStreaming.createSession('medium', 4)
  .then(() => audioStreaming.connectSocket())
  .catch(console.error);
```

### Error Handling & Recovery

#### Common Error Scenarios
1. **Authentication Failure**: Invalid or expired JWT token
2. **Session Timeout**: Session expires during streaming
3. **Audio Processing Error**: Invalid audio format or corrupted chunks
4. **Network Disconnection**: WebSocket connection lost
5. **Server Overload**: Too many concurrent sessions

#### Error Recovery Strategies
```javascript
// Implement retry logic for failed connections
const retryConnection = async (maxRetries = 3) => {
  for (let i = 0; i < maxRetries; i++) {
    try {
      await audioStreaming.createSession();
      audioStreaming.connectSocket();
      break;
    } catch (error) {
      console.log(`Retry ${i + 1}/${maxRetries}:`, error);
      await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
    }
  }
};

// Handle session expiration
socket.on('disconnect', (reason) => {
  if (reason === 'io server disconnect') {
    // Server disconnected the client, try to reconnect
    retryConnection();
  }
});
```

## � Data Models & Schemas

### Core Data Models

#### TaskItem Model
```json
{
  "id": "string (ObjectId)",
  "type": "single_choice|multiple_choice|image_identify|speak_word",
  "question": {
    "type": "single_choice",
    "text": "नेपालको राजधानी कुन हो?",
    "options": ["काठमाडौं", "पोखरा", "चितवन", "भक्तपुर"],
    "word": null,
    "media": [
      {
        "object_name": "audio_hint.mp3",
        "folder": "audio_hints",
        "media_type": "audio",
        "bucket_name": "nepali-app-media",
        "content_type": "audio/mpeg",
        "size_bytes": 15420,
        "original_filename": "hint.mp3"
      }
    ],
    "metadata": {}
  },
  "correct_answer": {
    "type": "single_choice",
    "value": "काठमाडौं",
    "media": [],
    "metadata": {}
  },
  "user_answer": {
    "type": "single_choice",
    "value": "काठमाडौं",
    "media": [],
    "metadata": {}
  },
  "status": "pending|completed|skipped|expired",
  "result": "correct|incorrect|partial|not_attempted",
  "remark": "Good job!",
  "created_at": "2024-01-01T12:00:00Z",
  "submitted_at": "2024-01-01T12:05:00Z",
  "verified_at": "2024-01-01T12:05:30Z",
  "answered_at": "2024-01-01T12:05:00Z",
  "total_score": 10,
  "scored": 10,
  "submitted": true,
  "attempts_count": 1,
  "metadata": {}
}
```

#### TaskSet Model
```json
{
  "id": "string (ObjectId)",
  "user_id": "string",
  "input_type": "text|audio|video|image",
  "input_content": "User provided text content",
  "input_data": null,
  "tasks": ["task_id_1", "task_id_2", "task_id_3"],
  "created_at": "2024-01-01T12:00:00Z",
  "submitted_at": "2024-01-01T12:10:00Z",
  "verified_at": "2024-01-01T12:11:00Z",
  "completed_at": "2024-01-01T12:10:00Z",
  "status": "pending|completed|skipped|expired",
  "difficulty": "easy|medium|hard",
  "total_tasks": 4,
  "attempted_tasks": 3,
  "total_score": 40,
  "scored": 30,
  "notes": "Generated from audio input",
  "remark": "Well done!",
  "metadata": {
    "generation_method": "gemini_audio",
    "processing_time_ms": 3500,
    "audio_duration_ms": 15000
  }
}
```

#### Question Model (Generic)
```json
{
  "type": "single_choice|multiple_choice|image_identify|speak_word",
  "text": "Question text in Nepali",
  "options": {
    "a": "Option A text",
    "b": "Option B text",
    "c": "Option C text",
    "d": "Option D text"
  },
  "word": "Word for pronunciation tasks",
  "media": [
    {
      "object_name": "question_image.jpg",
      "folder": "question_images",
      "media_type": "image",
      "bucket_name": "nepali-app-media",
      "content_type": "image/jpeg",
      "size_bytes": 245760,
      "original_filename": "kathmandu.jpg"
    }
  ],
  "metadata": {
    "difficulty_level": 2,
    "topic": "geography",
    "language": "nepali"
  }
}
```

#### Answer Model (Generic)
```json
{
  "type": "single_choice|multiple_choice|image_identify|speak_word",
  "value": "Answer value (string, array, or object)",
  "media": [
    {
      "object_name": "user_audio_response.wav",
      "folder": "user_responses",
      "media_type": "audio",
      "bucket_name": "nepali-app-media",
      "content_type": "audio/wav",
      "size_bytes": 32768,
      "original_filename": "pronunciation.wav"
    }
  ],
  "metadata": {
    "confidence_score": 0.95,
    "processing_time_ms": 150
  }
}
```

#### User Model
```json
{
  "id": "string (ObjectId)",
  "username": "john_doe",
  "email": "<EMAIL>",
  "role": {
    "id": "role_id",
    "name": "user"
  },
  "full_name": "John Doe",
  "profile_picture": "https://example.com/profile.jpg",
  "auth_provider": "password|google|both",
  "phone_number": "+**********",
  "country_code": "US",
  "created_at": "2024-01-01T00:00:00Z",
  "last_login": "2024-01-01T12:00:00Z"
}
```

#### AuthResponse Model
```json
{
  "id": "user_id_string",
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer",
  "username": "john_doe",
  "email": "<EMAIL>",
  "role": "user",
  "tenant_id": "tenant_123",
  "tenant_label": "Nepali App",
  "tenant_slug": "nepali_app",
  "full_name": "John Doe",
  "profile_picture": "https://example.com/profile.jpg",
  "auth_provider": "password",
  "last_login": "2024-01-01T12:00:00Z",
  "previous_login": "2024-01-01T08:00:00Z",
  "phone_number": "+**********",
  "country_code": "US"
}
```

## 🔄 Task Generation System

### Task Types
- **SINGLE_CHOICE**: Single correct answer from multiple options
- **MULTIPLE_CHOICE**: Multiple correct answers possible

### Difficulty Levels
- **Easy**: Basic vocabulary and simple concepts
- **Medium**: Intermediate complexity
- **Hard**: Advanced topics and complex scenarios

### Task Generation Flow
1. Audio input via Socket.IO
2. Real-time processing with Gemini AI
3. Task creation based on audio content
4. Storage in MongoDB
5. Real-time delivery to frontend

## 🗄️ Database Architecture & Data Models

### MongoDB Database Structure

The system uses a **multi-tenant MongoDB architecture** with optimized connection pooling:

#### Database Organization
- **Admin Database** (`nepali_app_admin`): Global system data
- **Tenant Databases** (`nepali_app_<tenant_id>`): Tenant-specific data

#### Connection Management
- **AsyncMongoClient**: API endpoints and real-time operations
- **MongoClient**: Background tasks and synchronous operations
- **Connection Pool**: 50 max connections, 5 min connections per service
- **Timeouts**: 30s server selection, 30s connection, 60s socket
- **Retry Logic**: Automatic retry with exponential backoff for failed operations
- **Optimizations**: Retryable reads/writes, primary-preferred read preference

### Admin Database Collections

#### Users Collection
```json
{
  "_id": "ObjectId",
  "username": "string (unique)",
  "email": "string (unique)",
  "password_hash": "string (Argon2)",
  "role": "user|admin|agent",
  "google_id": "string (optional)",
  "full_name": "string",
  "profile_picture": "string (URL)",
  "auth_provider": "password|google|both",
  "phone_number": "string",
  "country_code": "string",
  "created_at": "datetime",
  "last_login": "datetime",
  "previous_login": "datetime",
  "onboarding_completed": "boolean",
  "age": "integer",
  "difficulty_level": "integer (1-3)",
  "preferred_topics": ["string"]
}
```

#### Roles Collection
```json
{
  "_id": "ObjectId",
  "name": "string (unique)",
  "permissions": ["string"],
  "created_at": "datetime"
}
```

#### Tenants Collection
```json
{
  "_id": "ObjectId",
  "name": "string",
  "slug": "string (unique)",
  "client_id": "string (unique)",
  "created_at": "datetime",
  "active": "boolean"
}
```

### Tenant Database Collections

#### Task Sets Collection
```json
{
  "_id": "ObjectId",
  "user_id": "string",
  "input_type": "text|audio|video|image",
  "input_content": "string",
  "input_data": "object (optional)",
  "tasks": ["ObjectId"], // References to task_items
  "created_at": "datetime",
  "submitted_at": "datetime (optional)",
  "verified_at": "datetime (optional)",
  "completed_at": "datetime (optional)",
  "status": "pending|completed|skipped|expired",
  "difficulty": "easy|medium|hard",
  "total_tasks": "integer",
  "attempted_tasks": "integer",
  "total_score": "integer", // Maximum possible score
  "scored": "integer", // Actual score achieved
  "notes": "string (optional)",
  "remark": "string (optional)",
  "metadata": "object"
}
```

#### Task Items Collection
```json
{
  "_id": "ObjectId",
  "type": "SINGLE_CHOICE|MULTIPLE_CHOICE",
  "question": {
    "text": "string (Nepali)",
    "text_english": "string (English translation)",
    "options": {
      "a": "string",
      "b": "string",
      "c": "string",
      "d": "string"
    },
    "media": [
      {
        "object_name": "string",
        "folder": "string",
        "media_type": "image|audio|video|document|other",
        "bucket_name": "string",
        "content_type": "string",
        "size_bytes": "integer",
        "original_filename": "string"
      }
    ]
  },
  "correct_answer": {
    "value": "string|array", // "a" for single choice, ["a","b"] for multiple
    "media": ["object"],
    "metadata": "object"
  },
  "user_answer": {
    "value": "string|array",
    "media": ["object"],
    "metadata": "object"
  },
  "status": "pending|completed|skipped|expired",
  "result": "correct|incorrect|partial|not_attempted",
  "remark": "string",
  "created_at": "datetime",
  "submitted_at": "datetime",
  "verified_at": "datetime",
  "answered_at": "datetime",
  "total_score": "integer",
  "scored": "integer",
  "submitted": "boolean",
  "attempts_count": "integer",
  "complexity": "integer (1-5)",
  "metadata": "object"
}
```

## 📁 Project Structure & Organization

```
nepali_app/
├── app/
│   ├── shared/                    # Shared utilities and models
│   │   ├── models/               # Pydantic data models
│   │   │   ├── user.py          # User, UserTenantDB models
│   │   │   ├── task.py          # TaskItem, TaskSet, Question, Answer
│   │   │   ├── role.py          # Role management models
│   │   │   └── security.py      # Authentication models
│   │   ├── redis/               # Redis connection management
│   │   ├── socketio/            # Socket.IO implementation
│   │   │   ├── socketio_server.py    # Main Socket.IO server
│   │   │   ├── connection_manager.py # Connection lifecycle
│   │   │   ├── status_constants.py   # Event constants
│   │   │   └── task_utils.py         # Task generation utilities
│   │   ├── utils/               # Logging and helper utilities
│   │   ├── config.py            # Environment configuration
│   │   ├── database.py          # MongoDB connection management
│   │   └── security.py          # JWT, authentication, password hashing
│   ├── v1/api/                  # API version 1 - Microservices
│   │   ├── auth_service/        # Authentication & User Management
│   │   │   ├── routes/         # API endpoints
│   │   │   │   ├── auth.py     # Login, signup, OAuth
│   │   │   │   ├── users.py    # User management
│   │   │   │   └── roles.py    # Role management
│   │   │   └── __init__.py     # FastAPI app initialization
│   │   ├── socket_service/      # Real-time Socket.IO Communication
│   │   │   ├── routes/         # API endpoints
│   │   │   │   └── socket_auth.py  # Session management
│   │   │   └── __init__.py     # FastAPI app + Socket.IO server
│   │   └── management_service/  # CRUD Operations & Data Management
│   │       ├── routes/         # API endpoints
│   │       │   ├── tasks/      # Task management
│   │       │   │   ├── task_sets.py    # Task set operations
│   │       │   │   ├── task_items.py   # Task item operations
│   │       │   │   └── submissions.py  # Answer submissions
│   │       │   ├── scoring/    # Scoring and leaderboards
│   │       │   │   ├── scoring.py      # User scoring
│   │       │   │   └── leaderboard.py  # Leaderboard operations
│   │       │   └── media/      # Media file handling
│   │       │       └── media.py        # File upload/download
│   │       └── __init__.py     # FastAPI app initialization
│   └── __init__.py             # Main application entry point
├── docker/                     # Docker configuration
│   ├── Dockerfile.auth        # Auth service container
│   ├── Dockerfile.socket      # Socket service container
│   ├── Dockerfile.management  # Management service container
│   └── Dockerfile.base        # Base Python image
├── traefik/                   # Reverse proxy configuration
│   ├── traefik.yml           # Main Traefik configuration
│   └── dynamic/              # Dynamic routing configuration
├── docs/                      # Documentation
│   ├── api_docs.md           # API documentation
│   └── status-exchange-flow.md # Socket.IO flow documentation
├── compose.yml               # Docker Compose orchestration
├── pyproject.toml           # Python dependencies and project config
├── .env.example             # Environment variables template
└── README.md               # This file
```

## 🐳 Docker & Deployment Configuration

### Docker Compose Architecture

The system uses **Docker Compose** for orchestration with **Traefik** as the reverse proxy:

```yaml
# Key services in compose.yml
services:
  traefik:          # Reverse proxy and load balancer
  redis:            # Session store and Socket.IO backend
  auth:             # Authentication service (Port 8001)
  socket:           # Socket.IO service (Port 8002)
  management:       # Management service (Port 8003)
```

### Environment Configuration

#### Required Environment Variables
```bash
# Database
DB_URL=mongodb://localhost:27017
ADMIN_DB_NAME=nepali_app_admin

# Security
SECRET_KEY=your-secret-key-256-bits
ALGORITHM=HS256

# External Services
GEMINI_API_KEY=your-gemini-api-key
GOOGLE_CLIENT_ID=your-google-oauth-client-id

# Redis
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=your-redis-password

# Ports
API_PORT=8204
TRAEFIK_DASHBOARD_PORT=8205

# Logging
LOG_LEVEL=INFO
```

### Traefik Routing Configuration

#### Path-based Routing
- `/v1/auth/*` → Auth Service (Port 8001)
- `/v1/socket/*` → Socket Service (Port 8002)
- `/v1/management/*` → Management Service (Port 8003)

#### Load Balancing & Health Checks
- **Health Check Endpoints**: `/health` on each service
- **Load Balancing**: Round-robin across service instances
- **SSL/TLS**: Let's Encrypt integration for production
- **Domain Support**: `napp-api.nextai.asia` with automatic HTTPS

## 🛠️ Development Guidelines

### Adding New Services

1. **Create service directory**: `app/v1/api/new_service/`
2. **Implement FastAPI app**: Follow existing service patterns
3. **Add Dockerfile**: `docker/Dockerfile.new_service`
4. **Update compose.yml**: Add service configuration
5. **Configure Traefik**: Add routing rules

### Code Organization
- **Shared Components**: Place in `app/shared/`
- **Service-Specific**: Keep within service directories
- **Models**: Use Pydantic for validation
- **Database**: Async operations preferred
- **Logging**: Use `setup_new_logging(__name__)`

### Testing
- Run tests with `pytest`
- Test specific services in their respective directories
- Use OpenAPI schemas for request/response validation in tests

## 🔧 Troubleshooting

### Common Issues

**Service not responding**:
- Check container status: `docker-compose ps`
- View logs: `docker-compose logs service-name`
- Restart service: `docker-compose up -d --force-recreate service-name`

**Database connection issues**:
- Verify `DB_URL` in `.env`
- Check MongoDB accessibility
- Review connection pool settings

**Traefik routing problems**:
- Force recreate containers: `docker-compose up -d --force-recreate`
- Check Traefik dashboard: `http://localhost:8205`
- Verify service labels in `compose.yml`

**Socket.IO connection failures**:
- Verify `GEMINI_API_KEY` is set
- Check Redis connectivity
- Review audio chunk validation

### Performance Optimization
- **MongoDB**: Adjust connection pool sizes in `database.py`
- **Redis**: Configure memory limits in `compose.yml`
- **Docker**: Modify resource limits per service needs

## 🤝 Contributing

1. Follow existing code patterns and structure
2. Add tests for new functionality
3. Update documentation for API changes
4. Use type hints and Pydantic models
5. Implement proper error handling and logging

## 🔄 Task Generation System

### Task Types
- **SINGLE_CHOICE**: Single correct answer from multiple options
- **MULTIPLE_CHOICE**: Multiple correct answers possible
- **IMAGE_IDENTIFY**: Image-based identification tasks
- **SPEAK_WORD**: Audio pronunciation tasks

### Difficulty Levels
- **Easy**: Basic vocabulary and simple concepts
- **Medium**: Intermediate complexity
- **Hard**: Advanced topics and complex scenarios

### Task Generation Flow
1. Audio input via Socket.IO
2. Real-time processing with Gemini AI
3. Task creation based on audio content
4. Storage in MongoDB
5. Real-time delivery to frontend

## 📚 API Documentation & Endpoints

### Service Overview

| Service | Interactive Docs | OpenAPI JSON Schema |
|---------|------------------|-------------------|
| **Auth Service** | `http://localhost:8204/v1/auth/docs` | `http://localhost:8204/v1/auth/openapi.json` |
| **Socket Service** | `http://localhost:8204/v1/socket/docs` | `http://localhost:8204/v1/socket/openapi.json` |
| **Management Service** | `http://localhost:8204/v1/management/docs` | `http://localhost:8204/v1/management/openapi.json` |

### Complete API Reference

For detailed API documentation with request/response schemas, authentication flows, and integration examples, see:
- **[Complete API Documentation](docs/api_docs.md)** - Comprehensive API reference
- **[Socket.IO Flow Documentation](docs/status-exchange-flow.md)** - Real-time communication guide

---

## 🛠️ Development Guidelines

### Adding New Services

1. **Create service directory**: `app/v1/api/new_service/`
2. **Implement FastAPI app**: Follow existing service patterns
3. **Add Dockerfile**: `docker/Dockerfile.new_service`
4. **Update compose.yml**: Add service configuration
5. **Configure Traefik**: Add routing rules

### Code Organization
- **Shared Components**: Place in `app/shared/`
- **Service-Specific**: Keep within service directories
- **Models**: Use Pydantic for validation
- **Database**: Async operations preferred
- **Logging**: Use `setup_new_logging(__name__)`

### Testing
- Run tests with `pytest`
- Test specific services in their respective directories
- Use OpenAPI schemas for request/response validation in tests

## 🔧 Troubleshooting

### Common Issues

**Service not responding**:
- Check container status: `docker-compose ps`
- View logs: `docker-compose logs service-name`
- Restart service: `docker-compose up -d --force-recreate service-name`

**Database connection issues**:
- Verify `DB_URL` in `.env`
- Check MongoDB accessibility
- Review connection pool settings

**Traefik routing problems**:
- Force recreate containers: `docker-compose up -d --force-recreate`
- Check Traefik dashboard: `http://localhost:8205`
- Verify service labels in `compose.yml`

**Socket.IO connection failures**:
- Verify `GEMINI_API_KEY` is set
- Check Redis connectivity
- Review audio chunk validation

### Performance Optimization
- **MongoDB**: Adjust connection pool sizes in `database.py`
- **Redis**: Configure memory limits in `compose.yml`
- **Docker**: Modify resource limits per service needs

## 🤝 Contributing

1. Follow existing code patterns and structure
2. Add tests for new functionality
3. Update documentation for API changes
4. Use type hints and Pydantic models
5. Implement proper error handling and logging

## 🚀 Deployment Guide

### Production Environment Variables
```env
# Production Database
DB_URL=mongodb://prod-mongodb-cluster
ADMIN_DB_NAME=nepali_app_prod

# Security
SECRET_KEY=production-secret-key-256-bits
LOG_LEVEL=WARNING

# External Services
GEMINI_API_KEY=production-gemini-key
REDIS_PASSWORD=production-redis-password

# Scaling
API_PORT=80
TRAEFIK_DASHBOARD_PORT=8080
```

### Docker Production Build
```bash
# Build production images
docker-compose -f compose.yml build

# Deploy with production settings
docker-compose -f compose.yml up -d

# Scale services
docker-compose up -d --scale auth=2 --scale socket=2 --scale management=3
```

### Health Monitoring
```bash
# Check all service health
for service in auth socket management; do
  echo "Checking $service..."
  curl -f http://localhost:8204/v1/$service/health || echo "❌ $service unhealthy"
done

# Monitor logs
docker-compose logs -f --tail=100
```

## 📊 Performance Considerations

### Database Optimization
- Use indexes on frequently queried fields
- Implement connection pooling (configured in `database.py`)
- Consider read replicas for heavy read workloads

### Redis Optimization
- Configure memory limits based on usage patterns
- Use Redis clustering for high availability
- Implement proper key expiration strategies

### Service Scaling
- **Socket Service**: CPU-intensive (audio processing)
- **Auth Service**: Memory-intensive (JWT operations)
- **Management Service**: I/O-intensive (database operations)

---

## 🚀 Deployment Guide

### Production Environment Variables
```env
# Production Database
DB_URL=mongodb://prod-mongodb-cluster
ADMIN_DB_NAME=nepali_app_prod

# Security
SECRET_KEY=production-secret-key-256-bits
LOG_LEVEL=WARNING

# External Services
GEMINI_API_KEY=production-gemini-key
REDIS_PASSWORD=production-redis-password

# Scaling
API_PORT=80
TRAEFIK_DASHBOARD_PORT=8080
```

### Docker Production Build
```bash
# Build production images
docker-compose -f compose.yml build

# Deploy with production settings
docker-compose -f compose.yml up -d

# Scale services
docker-compose up -d --scale auth=2 --scale socket=2 --scale management=3
```

### Health Monitoring
```bash
# Check all service health
for service in auth socket management; do
  echo "Checking $service..."
  curl -f http://localhost:8204/v1/$service/health || echo "❌ $service unhealthy"
done

# Monitor logs
docker-compose logs -f --tail=100
```

## 📊 Performance Considerations

### Database Optimization
- Use indexes on frequently queried fields
- Implement connection pooling (configured in `database.py`)
- Consider read replicas for heavy read workloads

### Redis Optimization
- Configure memory limits based on usage patterns
- Use Redis clustering for high availability
- Implement proper key expiration strategies

### Service Scaling
- **Socket Service**: CPU-intensive (audio processing)
- **Auth Service**: Memory-intensive (JWT operations)
- **Management Service**: I/O-intensive (database operations)

## 📄 License

[Add your license information here]

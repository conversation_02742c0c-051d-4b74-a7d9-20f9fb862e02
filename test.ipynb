{"cells": [{"cell_type": "code", "execution_count": 1, "id": "6566457b", "metadata": {}, "outputs": [], "source": ["from pymongo import AsyncMongoClient"]}, {"cell_type": "code", "execution_count": null, "id": "46d8a834", "metadata": {}, "outputs": [], "source": ["from bson import ObjectId\n", "object_ids = [\n", "    ObjectId(\"683fd0c612e41f195c7a0750\"),\n", "    ObjectId(\"683fd0c612e41f195c7a0751\"),\n", "    ObjectId(\"683fd0c612e41f195c7a0752\"),\n", "]\n", "\n", "pipeline = [\n", "    {\"$match\": {\"_id\": {\"$in\": object_ids}}},\n", "    {\"$project\": {\n", "        \"question\": 1,\n", "        \"correct_answer\": 1\n", "    }}\n", "]"]}, {"cell_type": "code", "execution_count": 12, "id": "32ccc4d5", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_155349/3871963110.py:2: RuntimeWarning: coroutine 'AsyncCollection.aggregate' was never awaited\n", "  result =await client[\"task_items\"].aggregate(pipeline)\n", "RuntimeWarning: Enable tracemalloc to get the object allocation traceback\n"]}], "source": ["client = AsyncMongoClient(\"*************************************************")[\"nepali_app\"]\n", "result =await client[\"task_items\"].aggregate(pipeline)\n"]}, {"cell_type": "code", "execution_count": 13, "id": "2832fdf5", "metadata": {}, "outputs": [], "source": ["result_list = await result.to_list(length=None)"]}, {"cell_type": "code", "execution_count": 14, "id": "93813de8", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'_id': ObjectId('683fd0c612e41f195c7a0750'),\n", "  'question': {'text': \"अडियोमा कति पटक 'हेलो' शब्द प्रयोग भएको छ?\",\n", "   'translated_text': \"How many times is the word 'Hello' used in the audio?\",\n", "   'options': {'a': 'दुई', 'b': 'तीन', 'c': 'चार', 'd': 'पाँच'},\n", "   'answer_hint': 'हेलो',\n", "   'media_url': None,\n", "   'metadata': {}},\n", "  'correct_answer': {'value': 'b', 'type': 'single'}},\n", " {'_id': ObjectId('683fd0c612e41f195c7a0751'),\n", "  'question': {'text': 'अडियोमा कसले बोलिरहेको छ?',\n", "   'translated_text': 'Who is speaking in the audio?',\n", "   'options': {'a': 'शिक्षक',\n", "    'b': 'विद्यार्थी',\n", "    'c': 'एक व्यक्ति',\n", "    'd': 'दुई व्यक्ति'},\n", "   'answer_hint': 'व्यक्ति',\n", "   'media_url': None,\n", "   'metadata': {}},\n", "  'correct_answer': {'value': 'd', 'type': 'single'}},\n", " {'_id': ObjectId('683fd0c612e41f195c7a0752'),\n", "  'question': {'text': 'यो तस्वीरमा के देखिन्छ?',\n", "   'translated_text': 'What is seen in this picture?',\n", "   'options': {'a': 'किताब', 'b': 'घर', 'c': 'फोन', 'd': 'कम्प्युटर'},\n", "   'answer_hint': \"I will generate an image of a phone. The image will depict a modern smartphone with a sleek design, resting on a textured wooden surface. The phone's screen will be illuminated, displaying a vibrant and colorful abstract wallpaper. The camera lens on the back will be subtly visible, and the overall lighting will create soft highlights and shadows, emphasizing the phone's form and materials.\\n\\n\",\n", "   'media_url': '68391d86b8b0e7ec9ababfbb/imagen/फोन.jpg',\n", "   'metadata': {'object_name': '68391d86b8b0e7ec9ababfbb/imagen/फोन.jpg',\n", "    'bucket_name': 'nepali.test',\n", "    'object_path': '68391d86b8b0e7ec9ababfbb/imagen/फोन.jpg',\n", "    'file_name': 'फोन.jpg',\n", "    'url': 'https://minio.nextai.asia/nepali.test/68391d86b8b0e7ec9ababfbb/imagen/%E0%A4%AB%E0%A5%8B%E0%A4%A8.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=minio-admin%2F20250604%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250604T045124Z&X-Amz-Expires=86400&X-Amz-SignedHeaders=host&X-Amz-Signature=b3dd7c87fc3093a4028dae461d76a48ab643f8bc54f6b93a19101ab449ce9a4b',\n", "    'content_type': 'image/jpeg',\n", "    'size_bytes': 1288020,\n", "    'user_id': '68391d86b8b0e7ec9ababfbb',\n", "    'folder': 'imagen',\n", "    'session_id': None,\n", "    'created_at': '2025-06-04T04:51:24.090876+00:00',\n", "    'file_extension': '.jpg'}},\n", "  'correct_answer': {'value': 'c', 'type': 'multiple'}}]"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["result_list "]}, {"cell_type": "code", "execution_count": 19, "id": "003831f8", "metadata": {}, "outputs": [], "source": ["tasks = \"\"\n", "for task in result_list:\n", "        task[\"question\"].pop(\"media_url\", None)\n", "        task[\"question\"].pop(\"metadata\", None)\n", "        tasks += f\"Question: {task['question']}\\nAnswer: {task['correct_answer']}\\n\\n\"\n"]}, {"cell_type": "code", "execution_count": 20, "id": "68dea5cc", "metadata": {}, "outputs": [{"data": {"text/plain": ["'Question: {\\'text\\': \"अडियोमा कति पटक \\'हेलो\\' शब्द प्रयोग भएको छ?\", \\'translated_text\\': \"How many times is the word \\'Hello\\' used in the audio?\", \\'options\\': {\\'a\\': \\'दुई\\', \\'b\\': \\'तीन\\', \\'c\\': \\'चार\\', \\'d\\': \\'पाँच\\'}, \\'answer_hint\\': \\'हेलो\\'}\\nAnswer: {\\'value\\': \\'b\\', \\'type\\': \\'single\\'}\\n\\nQuestion: {\\'text\\': \\'अडियोमा कसले बोलिरहेको छ?\\', \\'translated_text\\': \\'Who is speaking in the audio?\\', \\'options\\': {\\'a\\': \\'शिक्षक\\', \\'b\\': \\'विद्यार्थी\\', \\'c\\': \\'एक व्यक्ति\\', \\'d\\': \\'दुई व्यक्ति\\'}, \\'answer_hint\\': \\'व्यक्ति\\'}\\nAnswer: {\\'value\\': \\'d\\', \\'type\\': \\'single\\'}\\n\\nQuestion: {\\'text\\': \\'यो तस्वीरमा के देखिन्छ?\\', \\'translated_text\\': \\'What is seen in this picture?\\', \\'options\\': {\\'a\\': \\'किताब\\', \\'b\\': \\'घर\\', \\'c\\': \\'फोन\\', \\'d\\': \\'कम्प्युटर\\'}, \\'answer_hint\\': \"I will generate an image of a phone. The image will depict a modern smartphone with a sleek design, resting on a textured wooden surface. The phone\\'s screen will be illuminated, displaying a vibrant and colorful abstract wallpaper. The camera lens on the back will be subtly visible, and the overall lighting will create soft highlights and shadows, emphasizing the phone\\'s form and materials.\\\\n\\\\n\"}\\nAnswer: {\\'value\\': \\'c\\', \\'type\\': \\'multiple\\'}\\n\\n'"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["tasks"]}, {"cell_type": "code", "execution_count": null, "id": "c038eab5", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "nepali-app", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 5}
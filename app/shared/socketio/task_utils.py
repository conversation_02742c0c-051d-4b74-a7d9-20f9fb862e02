"""
Utility functions for saving tasks from prompt_maker.py output format.
Clean and optimized for async operations.
"""

from datetime import datetime, timezone
from typing import Dict, Any, List, Optional
from bson.objectid import ObjectId
import asyncio

from app.shared.db_enums import TaskStatus, QuizType, InputType
from app.shared.utils.logger import setup_new_logging
from app.shared.models.user import UserTenantDB
# Import generate function lazily to avoid circular imports

# Configure logging
logger = setup_new_logging(__name__)

# Retry configuration
MAX_RETRIES = 3
INITIAL_RETRY_DELAY = 1.0
MAX_RETRY_DELAY = 30.0


async def retry_with_exponential_backoff(func, *args, **kwargs):
    """Execute function with exponential backoff retry logic."""
    delay = INITIAL_RETRY_DELAY
    last_exception = None

    for attempt in range(MAX_RETRIES):
        try:
            return await func(*args, **kwargs)
        except Exception as e:
            last_exception = e
            if attempt == MAX_RETRIES - 1:
                break

            logger.debug(f"Attempt {attempt + 1} failed: {e}. Retrying in {delay}s")
            await asyncio.sleep(delay)
            delay = min(delay * 2, MAX_RETRY_DELAY)

    logger.error(f"All {MAX_RETRIES} attempts failed: {last_exception}")
    raise last_exception





async def save_task_set_and_items(
    current_user: Any,
    session_id: str,
    tasks_data: Dict[str, Any],
    task_set_id: Optional[str] = None,
    audio_storage_info: Optional[Dict[str, Any]] = None,
    socketio_server: Any = None,
    use_background_tasks: bool = True
) -> Dict[str, Any]:
    """
    Save task set and task items from prompt_maker.py output format.

    Args:
        current_user: Current user context with database access
        session_id: Session identifier
        tasks_data: Output from prompt_maker.py in format {"tasks": [...]}
        task_set_id: Optional existing task set ID, creates new one if None
        audio_storage_info: Optional MinIO storage information for the audio file
        socketio_server: Optional SocketIO server for real-time updates
        use_background_tasks: If True (socket), use background tasks for parallel processing
                             If False (HTTP), process remaining tasks in real-time after return

    Returns:
        Dictionary with task_set_id, task_count, and status
    """
    try:
        # Extract tasks from prompt_maker output
        tasks = tasks_data.get("tasks", [])
        if not tasks:
            logger.warning(f"No tasks found in prompt_maker output for session {session_id}")
            return {
                "task_count": 0,
                "status": "error",
                "error": "No tasks generated",
                "message": "No tasks generated from the provided input"
            }

        # Create or use existing task set ID
        if not task_set_id:
            task_set_id = str(ObjectId())
            create_new_task_set = True
        else:
            create_new_task_set = False

        # Extract title from tasks_data if available, otherwise use a default
        title = tasks_data.get('title', 'Untitled Task Set')
        
        # Prepare task set update data
        task_set_update = {
            "title": title,
            "input_type": InputType.AUDIO.value,
            "status": TaskStatus.COMPLETED.value,
            "total_tasks": len(tasks),
            "attempted_tasks": 0,  # Count of task items that have been submitted at least once
            "total_score": 0,  # Will be calculated from tasks (sum of all task items' max scores)
            "scored": 0,  # User's achieved score (sum of all submitted task items' scores)
            "source": "prompt_maker_audio"
        }

        # Add usage metadata if available
        usage_metadata = tasks_data.get("usage_metadata", {})
        if usage_metadata:
            # Convert metadata to dict if it's not already
            if hasattr(usage_metadata, 'model_dump'):
                meta_dict = usage_metadata.model_dump()
            elif hasattr(usage_metadata, 'dict'):
                meta_dict = usage_metadata.dict()
            else:
                meta_dict = dict(usage_metadata) if usage_metadata else {}

            # Add type, input_type, and output_type fields for better tracking
            usage_metadata_with_type = {
                **meta_dict,
                "type": "audio",
                "input_type": "audio",
                "output_type": "text"
            }
            task_set_update["usage"] = usage_metadata_with_type
            logger.debug(f"Storing audio usage metadata in task set")
        else:
            logger.debug("No usage metadata available for audio processing")

        # Add audio storage information if provided
        if audio_storage_info:
            # Store complete MinIO object information in input_content
            input_content = {
                "object_name": audio_storage_info.get("object_name"),
                "bucket_name": audio_storage_info.get("bucket_name"),
                "object_path": audio_storage_info.get("object_path"),
                "file_name": audio_storage_info.get("file_name"),
                "content_type": audio_storage_info.get("content_type", "audio/wav"),
                "size_bytes": audio_storage_info.get("size_bytes"),
                "folder": "recordings",
                "session_id": session_id,
                "created_at": audio_storage_info.get("created_at"),
                "file_extension": audio_storage_info.get("file_extension", ".wav"),
            }
            task_set_update["input_content"] = input_content
            logger.debug(f"Adding input_content to task set")
        else:
            logger.debug("No audio_storage_info provided, input_content will not be added")


        # Prepare task items and calculate total score
        task_items = []
        total_max_score = 0

        # Process all tasks to create task items (save immediately without image generation)
        for task_index, task in enumerate(tasks):
            # Extract task data with proper mapping
            original_task_type = task.get("type", "single_choice")
            task_type = _map_task_type(original_task_type)
            question_data = task.get("question", {})
            story_data = task.get("story", {})  # Extract story data for story-based tasks
            total_score = task.get("total_score", 10)
            complexity = task.get("complexity", 1)
            
            # Log task type information
            logger.debug(f"Processing task {task_index + 1}/{len(tasks)} - Original type: '{original_task_type}', Mapped type: '{task_type}'")
            
            # Ensure complexity is an integer (handle any legacy dict format)
            if isinstance(complexity, dict):
                complexity = 2  # Default to medium if still dict format
            elif not isinstance(complexity, int):
                complexity = int(complexity) if str(complexity).isdigit() else 1

            # Create task item document (without _id - let MongoDB generate it)
            task_item = {
                "_id": ObjectId(),
                "task_set_id": ObjectId(task_set_id),
                "user_id": ObjectId(current_user.user.id),
                "type": task_type,
                "title": task.get("title", ""),  # Add title from task data
                "question": {
                    "text": question_data.get("text", ""),
                    "translated_text": question_data.get("translated_text", ""),
                    "options": question_data.get("options", {}),
                    "answer_hint": question_data.get("answer_hint", ""),
                    "media_url": question_data.get("media_url"),
                    "metadata": question_data.get("metadata", {})
                },
                "correct_answer": {
                    "value": question_data.get("answer", ""),
                    "type": "single" if task_type == QuizType.SINGLE_CHOICE.value else
                           "speak" if task_type == QuizType.SPEAK_WORD.value else "multiple"
                },
                "user_answer": None,
                "status": TaskStatus.PENDING.value,
                "result": None,
                "total_score": total_score,
                "scored": 0,  # User's achieved score for this individual item (0 by default)
                "submitted": False,  # Boolean flag indicating if the item has been submitted
                "submitted_at": None,  # Timestamp of first submission
                "complexity": complexity,
                "created_at": datetime.now(timezone.utc),
                "is_attempted": False,
                "attempts_count": 0
            }

            # Add story data if present (for story-based tasks)
            if story_data:
                task_item["story"] = {
                    "stage": story_data.get("stage", 1),
                    "script": story_data.get("script", ""),
                    "image": story_data.get("image", ""),
                    "media_url": story_data.get("media_url", ""),
                    "metadata": story_data.get("metadata", {})
                }

            task_items.append(task_item)
            total_max_score += total_score

        # Update task set with calculated total score
        task_set_update["total_score"] = total_max_score

        # Get user's difficulty level from profile (already stored as integer) - with retry logic
        user_profile = await retry_with_exponential_backoff(
            current_user.async_db.users.find_one,
            {"_id": ObjectId(current_user.user.id)},
            {"difficulty_level": 1}
        )
        difficulty_level = user_profile.get("difficulty_level") if user_profile else 2  # Default to medium (2)

        # Create or update task set with error handling
        try:
            logger.debug(f"Task set update data keys: {list(task_set_update.keys())}")

            # Database operations with retry logic - no semaphores for scalability
            if create_new_task_set:
                # Create new task set document
                task_set_doc = {
                    "_id": ObjectId(task_set_id),
                    "user_id": ObjectId(current_user.user.id),
                    "created_at": datetime.now(timezone.utc),
                    "session_id": session_id,
                    "difficulty_level": difficulty_level,
                    **task_set_update
                }
                await retry_with_exponential_backoff(
                    current_user.async_db.task_sets.insert_one,
                    task_set_doc
                )
                logger.info(f"Created new task set {task_set_id} with {len(tasks)} tasks")
            else:
                # Update existing task set
                result = await retry_with_exponential_backoff(
                    current_user.async_db.task_sets.update_one,
                    {"_id": ObjectId(task_set_id)},
                    {"$set": task_set_update}
                )
                if result.matched_count == 0:
                    logger.warning(f"Task set {task_set_id} not found, creating new one")
                    # Fallback: create new task set if update failed
                    task_set_doc = {
                        "_id": ObjectId(task_set_id),
                        "user_id": ObjectId(current_user.user.id),
                        "created_at": datetime.now(timezone.utc),
                        "session_id": session_id,
                        **task_set_update
                    }
                    await retry_with_exponential_backoff(
                        current_user.async_db.task_sets.insert_one,
                        task_set_doc
                    )
                    logger.info(f"Created fallback task set {task_set_id} with {len(tasks)} tasks")
                else:
                    logger.info(f"Updated existing task set {task_set_id} with {len(tasks)} tasks")
        except Exception as task_set_error:
            # Handle duplicate key error or other database errors
            if "duplicate key error" in str(task_set_error).lower():
                logger.warning(f"Task set {task_set_id} already exists, updating instead")
                # Try to update the existing task set
                await current_user.async_db.task_sets.update_one(
                    {"_id": ObjectId(task_set_id)},
                    {"$set": task_set_update}
                )
                logger.info(f"Updated existing task set {task_set_id} after duplicate key error")
            else:
                logger.error(f"Error handling task set {task_set_id}: {task_set_error}")
                raise task_set_error

        # Step 2: Sort tasks and task items to prioritize single/multiple choice first
        prioritized_tasks = _prioritize_tasks(tasks)

        # Debug: Log first task details
        if prioritized_tasks:
            first_task = prioritized_tasks[0]
            logger.info(f"First task type: {first_task.get('type')}")
            logger.debug(f"First task story data: {first_task.get('story', {})}")
            logger.debug(f"First task question data: {first_task.get('question', {})}")

        # Create a mapping from original task index to prioritized index
        task_index_mapping = {}
        for new_index, task in enumerate(prioritized_tasks):
            original_index = tasks.index(task)
            task_index_mapping[original_index] = new_index

        # Sort task_items according to the prioritized order
        sorted_task_items = []
        for task in prioritized_tasks:
            original_index = tasks.index(task)
            sorted_task_items.append(task_items[original_index])

        # Prepare sorted task items and collect their IDs
        item_ids = []
        for item in sorted_task_items:
            # Ensure each item has an _id (let MongoDB generate if not present)
            if "_id" not in item:
                item["_id"] = ObjectId()
            elif not isinstance(item["_id"], ObjectId):
                item["_id"] = ObjectId(item["_id"])

            # Add to item_ids list
            item_ids.append(item["_id"])

        logger.info(f"Sorted {len(task_items)} task items by priority")

        # Step 3: Insert sorted task items into task_items collection using bulk operation with retry logic
        if sorted_task_items:
            await retry_with_exponential_backoff(
                current_user.async_db.task_items.insert_many,
                sorted_task_items
            )
            logger.info(f"Inserted {len(sorted_task_items)} sorted task items for task set {task_set_id}")

        # Step 4: Update task set with array of task item IDs and total_score (establishing one-to-many relationship)
        # Use $set to update only specific fields without overwriting existing data
        final_update = {
            "tasks": item_ids,
            "scored": 0,  # Initialize scored to 0
            "total_score": total_max_score,  # Update total_score with calculated value
            "total_tasks": len(item_ids)  # Update total_tasks count
        }

        await retry_with_exponential_backoff(
            current_user.async_db.task_sets.update_one,
            {"_id": ObjectId(task_set_id)},
            {"$set": final_update}
        )
        logger.info(f"Updated task set {task_set_id} with {len(item_ids)} task item references")

        # USE NEW OPTIMIZED PROCESSOR
        try:
            from app.shared.socketio.optimized_task_processor import process_tasks_optimized

            if prioritized_tasks:
                await process_tasks_optimized(
                    current_user=current_user,
                    task_set_id=task_set_id,
                    prioritized_tasks=prioritized_tasks,
                    item_ids=item_ids,
                    socketio_server=socketio_server,
                    use_background_tasks=use_background_tasks
                )

        except Exception as e:
            logger.error(f"Task processing failed: {e}")

        return {
            "task_set_id": task_set_id,
            "task_count": len(tasks),
            "status": "completed",
            "message": f"Successfully saved {len(tasks)} tasks",
            "total_score": total_max_score,
            "has_generation_tasks": any(task.get("type") in ["image_identification", "image_identify", "speak_word"] for task in tasks)
        }

    except Exception as e:
        logger.error(f"Error saving task set and items for session {session_id}: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")

        return {
            "task_count": 0,
            "status": "error",
            "error": f"Error saving tasks: {str(e)}",
            "message": f"Failed to save tasks due to error: {str(e)}"
        }


def _map_task_type(prompt_maker_type: str) -> str:
    """
    Map prompt_maker.py task types to database QuizType enum values.

    Args:
        prompt_maker_type: Task type from prompt_maker.py

    Returns:
        Mapped QuizType enum value
    """
    type_mapping = {
        "single_choice": QuizType.SINGLE_CHOICE.value,
        "multiple_choice": QuizType.MULTIPLE_CHOICE.value,
        "image_identification": QuizType.IMAGE_IDENTIFICATION.value,  # Fixed: was "image_identify"
        "image_identify": QuizType.IMAGE_IDENTIFICATION.value,  # Keep old mapping for compatibility
        "speak_word": QuizType.SPEAK_WORD.value,
        # "audio_identification": QuizType.AUDIO_IDENTIFICATION.value,
        # "text_completion": QuizType.TEXT_COMPLETION.value
    }

    mapped_type = type_mapping.get(prompt_maker_type, QuizType.SINGLE_CHOICE.value)

    # Log the mapping for debugging
    if prompt_maker_type not in type_mapping:
        logger.warning(f"Unknown task type '{prompt_maker_type}', defaulting to single_choice")
    else:
        logger.debug(f"Mapped task type '{prompt_maker_type}' to '{mapped_type}'")

    return mapped_type


def convert_to_socketio_format(tasks_data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    Convert prompt_maker.py output to Socket.IO expected format.
    Supports both regular tasks and story-based tasks.

    Args:
        tasks_data: Output from prompt_maker.py in format {"tasks": [...]}

    Returns:
        List of tasks in Socket.IO format
    """
    try:
        tasks = tasks_data.get("tasks", [])
        socketio_tasks = []

        for task in tasks:
            question_data = task.get("question", {})
            story_data = task.get("story", {})

            socketio_task = {
                "id": str(ObjectId()),
                "type": task.get("type", "single_choice"),
                "question": question_data.get("text", ""),
                "translated_question": question_data.get("translated_text", ""),
                "options": question_data.get("options", {}),
                "answer": question_data.get("answer", ""),
                "answer_hint": question_data.get("answer_hint", ""),
                "media_url": question_data.get("media_url"),
                "metadata": question_data.get("metadata", {}),
                "total_score": task.get("total_score", 10),
                "complexity": task.get("complexity", 1),
                "user_answer": None,
                "created_at": datetime.now(timezone.utc).isoformat()
            }

            # Add story data if present (for story-based tasks)
            if story_data:
                socketio_task["story"] = {
                    "stage": story_data.get("stage", 1),
                    "script": story_data.get("script", ""),
                    "image": story_data.get("image", ""),
                    "media_url": story_data.get("media_url", ""),
                    "metadata": story_data.get("metadata", {})
                }

            socketio_tasks.append(socketio_task)

        return socketio_tasks

    except Exception as e:
        logger.error(f"Error converting tasks to Socket.IO format: {e}")
        return []


async def process_audio_with_prompt_maker(
    current_user: UserTenantDB,
    audio_bytes: bytes,
    num_tasks: int = 4,
) -> Dict[str, Any]:
    """
    Process audio using the new prompt_maker.py and return parsed tasks.

    Args:
        current_user: Current user context
        audio_bytes: Combined audio data
        num_tasks: Number of tasks to generate

    Returns:
        Parsed tasks data from prompt_maker.py
    """
    try:

        logger.info(f"Processing {len(audio_bytes)} bytes of audio with prompt_maker")

        # Import generate function lazily to avoid circular imports
        from app.v1.api.socket_service.generator.prompt_maker import generate

        # Call the new prompt_maker.py generate function with retry logic
        # Note: generate now returns a dictionary with tasks, title, and usage_metadata
        result = await retry_with_exponential_backoff(
            generate,
            audio_bytes,
            num_tasks,
            current_user
        )
        
        if not result or not result.get("tasks"):
            error_msg = "No task items returned from prompt_maker.generate"
            logger.error(error_msg)
            return {
                "tasks": [],
                "error": error_msg,
                "status": "error",
                "usage_metadata": result.get("usage_metadata", {}) if result else {}
            }

        # Use the result directly as it already contains tasks, title, and usage_metadata
        tasks_data = result
        logger.info(f"Successfully generated {len(tasks_data['tasks'])} tasks from prompt_maker")

        # Get usage_metadata from result if available
        usage_metadata = result.get('usage_metadata', {})

        # Log usage metadata for monitoring
        if usage_metadata:
            logger.debug(f"Audio processing completed with metadata")

        # Log first task for debugging
        if tasks_data.get('tasks'):
            first_task = tasks_data['tasks'][0]
            question_text = first_task.get('question', {}).get('text', '') if isinstance(first_task.get('question'), dict) else str(first_task.get('question', ''))
            logger.info(f"First task preview: type={first_task.get('type')}, "
                       f"question={question_text[:50]}...")

        return tasks_data

    except Exception as e:
        logger.error(f"Error processing audio with prompt_maker: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return {
            "tasks": [],
            "error": f"Error processing audio: {str(e)}",
            "status": "error",
            "usage_metadata": {}
        }


def _prioritize_tasks(tasks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    AGGRESSIVELY prioritize tasks with single choice + story image first at ANY COST.
    This ensures the first task will ALWAYS be single choice with story image if available.

    Priority order:
    1. Single choice WITH story image (HIGHEST PRIORITY)
    2. Other single choice tasks
    3. Multiple choice tasks
    4. All other task types

    Args:
        tasks: List of task data

    Returns:
        Prioritized list of tasks with single choice + story image GUARANTEED first
    """
    try:
        single_choice_with_story = []
        single_choice_without_story = []
        multiple_choice_tasks = []
        other_tasks = []

        for task in tasks:
            task_type = task.get("type", "")
            story_data = task.get("story", {})
            has_story_image = story_data and story_data.get("image")

            if task_type == "single_choice":
                if has_story_image:
                    single_choice_with_story.append(task)
                else:
                    single_choice_without_story.append(task)
            elif task_type == "multiple_choice":
                multiple_choice_tasks.append(task)
            else:
                other_tasks.append(task)

        # AGGRESSIVE PRIORITY: single choice with story image MUST be first
        prioritized = (single_choice_with_story + single_choice_without_story +
                      multiple_choice_tasks + other_tasks)

        logger.info(f"🎯 AGGRESSIVE task prioritization: {len(single_choice_with_story)} single+story (PRIORITY), "
                   f"{len(single_choice_without_story)} single, {len(multiple_choice_tasks)} multiple, "
                   f"{len(other_tasks)} others")

        # CRITICAL: Log first task details for verification
        if prioritized:
            first_task = prioritized[0]
            first_story = first_task.get("story", {})
            first_type = first_task.get('type')
            has_story_image = bool(first_story.get('image'))

            if first_type == 'single_choice' and has_story_image:
                logger.info(f"✅ PERFECT: First task is single_choice with story image - OPTIMAL for priority processing")
            elif first_type == 'single_choice':
                logger.warning(f"⚠️  SUBOPTIMAL: First task is single_choice but NO story image")
            else:
                logger.warning(f"⚠️  SUBOPTIMAL: First task is NOT single_choice (type: '{first_type}')")

            logger.info(f"🎯 First task confirmed: type='{first_type}', has_story_image={has_story_image}")
        else:
            logger.warning("❌ No tasks to prioritize!")

        return prioritized

    except Exception as e:
        logger.error(f"❌ Error prioritizing tasks: {e}")
        return tasks  # Return original order if error

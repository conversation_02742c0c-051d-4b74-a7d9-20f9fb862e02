"""
Production-grade MongoDB connection manager for multi-tenant applications.

This module provides a robust, thread-safe database connection manager
with proper connection pooling, error handling, and tenant isolation.
"""

import asyncio
import os
import time
from typing import Dict, Tuple, Optional, Any, Callable
from contextlib import asynccontextmanager
from dataclasses import dataclass
from threading import Lock
from functools import wraps

from pymongo import MongoClient, AsyncMongoClient
from pymongo.errors import ConnectionFailure, ServerSelectionTimeoutError
from pymongo.server_api import Server<PERSON><PERSON>
from bson import ObjectId
from fastapi import HTTPException

from app.shared.utils.logger import setup_new_logging

loggers = setup_new_logging(__name__)


def retry_db_operation(max_retries: int = 3, delay: float = 1.0, backoff: float = 2.0):
    """
    Decorator to retry database operations with exponential backoff.

    Args:
        max_retries: Maximum number of retry attempts
        delay: Initial delay between retries in seconds
        backoff: Backoff multiplier for delay
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            last_exception = None
            current_delay = delay

            for attempt in range(max_retries + 1):
                try:
                    return await func(*args, **kwargs)
                except (ConnectionFailure, ServerSelectionTimeoutError, Exception) as e:
                    last_exception = e
                    if attempt == max_retries:
                        loggers.error(f"Database operation failed after {max_retries} retries: {e}")
                        raise e

                    loggers.warning(f"Database operation failed (attempt {attempt + 1}/{max_retries + 1}): {e}. Retrying in {current_delay}s...")
                    await asyncio.sleep(current_delay)
                    current_delay *= backoff

            raise last_exception
        return wrapper
    return decorator


@dataclass
class DatabaseConfig:
    """Database configuration settings."""
    connection_url: str
    admin_db_name: str

    def get_optimized_client_options(self) -> dict:
        """Get optimized MongoDB client options for production workloads."""
        return {
            'serverSelectionTimeoutMS': 60000,  # 30 seconds for server selection
            'connectTimeoutMS': 60000,          # 30 seconds for initial connection
            'socketTimeoutMS': 90000,           # 60 seconds for socket operations
            'maxPoolSize': 100,                  # Maximum connections in pool
            'minPoolSize': 20,                   # Minimum connections in pool
            'maxIdleTimeMS': 600000,            # 5 minutes max idle time
            'retryWrites': True,                # Enable retryable writes
            'retryReads': True,                 # Enable retryable reads
            'w': 'majority',                    # Write concern
            'readPreference': 'primaryPreferred', # Read preference
            'appName': 'NepaliApp',             # Application name for monitoring
            'server_api': ServerApi('1')        # Use stable API version
        }


@dataclass
class TenantInfo:
    """Tenant information container."""
    tenant_id: str
    db_name: str
    name: Optional[str] = None
    slug: Optional[str] = None


class DatabaseConnectionManager:
    """
    Production-grade database connection manager with proper connection pooling
    and tenant isolation.
    """

    def __init__(self, config: DatabaseConfig):
        self.config = config
        self._admin_client: Optional[AsyncMongoClient] = None
        self._tenant_clients: Dict[str, Dict[str, Any]] = {}
        self._lock = Lock()
        self._initialized = False

    async def initialize(self) -> None:
        """Initialize the database connection manager."""
        if self._initialized:
            return

        try:
            # Get optimized client options
            client_options = self.config.get_optimized_client_options()

            # Initialize admin client with optimized settings
            self._admin_client = AsyncMongoClient(
                self.config.connection_url,
                **client_options
            )

            # Test connection with timeout
            await asyncio.wait_for(
                self._admin_client.admin.command('ping'),
                timeout=30.0
            )
            self._initialized = True
            loggers.info("Database connection manager initialized successfully with optimized settings")

        except Exception as e:
            loggers.error(f"Failed to initialize database connection manager: {e}")
            raise ConnectionFailure(f"Database initialization failed: {e}")

    async def close(self) -> None:
        """Close all database connections."""
        try:
            # Close admin client
            if self._admin_client:
                await self._admin_client.aclose()
                self._admin_client = None

            # Close tenant clients
            with self._lock:
                for tenant_id, clients in self._tenant_clients.items():
                    if 'async_client' in clients:
                        await clients['async_client'].aclose()
                    if 'sync_client' in clients:
                        clients['sync_client'].close()
                self._tenant_clients.clear()

            self._initialized = False
            loggers.info("Database connections closed successfully")

        except Exception as e:
            loggers.error(f"Error closing database connections: {e}")

    @asynccontextmanager
    async def get_admin_db(self):
        """
        Get admin database connection with proper context management.

        Yields:
            Admin database instance
        """
        if not self._initialized:
            await self.initialize()

        if not self._admin_client:
            raise ConnectionFailure("Admin client not initialized")

        try:
            db = self._admin_client[self.config.admin_db_name]
            yield db
        except Exception as e:
            loggers.error(f"Error accessing admin database: {e}")
            raise

    async def get_tenant_db(self, tenant_id: str) -> Tuple[Any, Any]:
        """
        Get tenant database connections with proper connection pooling.

        Args:
            tenant_id: The tenant ID

        Returns:
            Tuple of (sync_db, async_db) for the tenant
        """
        if not self._initialized:
            await self.initialize()

        # Check cache first
        with self._lock:
            if tenant_id in self._tenant_clients:
                clients = self._tenant_clients[tenant_id]
                return clients['sync_db'], clients['async_db']

        try:
            # Get tenant info from admin database
            async with self.get_admin_db() as admin_db:
                tenant_info = await admin_db.tenants.find_one({"_id": ObjectId(tenant_id)})

                if not tenant_info:
                    raise ValueError(f"No tenant found with ID: {tenant_id}")

                tenant_db_name = tenant_info["db_name"]

            # Get optimized client options
            client_options = self.config.get_optimized_client_options()

            # Create new connections for this tenant with optimized settings
            sync_client = MongoClient(
                self.config.connection_url,
                **client_options
            )
            async_client = AsyncMongoClient(
                self.config.connection_url,
                **client_options
            )

            # Test async connection with timeout
            await asyncio.wait_for(
                async_client.admin.command('ping'),
                timeout=30.0
            )

            # Cache the connections
            with self._lock:
                self._tenant_clients[tenant_id] = {
                    'sync_client': sync_client,
                    'async_client': async_client,
                    'sync_db': sync_client[tenant_db_name],
                    'async_db': async_client[tenant_db_name],
                    'db_name': tenant_db_name
                }

            return sync_client[tenant_db_name], async_client[tenant_db_name]

        except Exception as e:
            loggers.error(f"Error getting tenant database for {tenant_id}: {e}")
            # Clean up on error
            with self._lock:
                if tenant_id in self._tenant_clients:
                    del self._tenant_clients[tenant_id]
            raise HTTPException(status_code=500, detail=f"Database connection error: {e}")

    async def get_tenant_info_by_slug(self, slug: str) -> TenantInfo:
        """
        Get tenant information by slug.

        Args:
            slug: The tenant slug

        Returns:
            TenantInfo object
        """
        if not slug:
            raise ValueError("Slug cannot be empty")

        try:
            async with self.get_admin_db() as admin_db:
                tenant_data = await admin_db.tenants.find_one(
                    {"slug": slug},
                    {"_id": 1, "name": 1, "db_name": 1}
                )

                if not tenant_data:
                    raise ValueError(f"No tenant found with slug: {slug}")

                return TenantInfo(
                    tenant_id=str(tenant_data["_id"]),
                    db_name=tenant_data["db_name"],
                    name=tenant_data.get("name"),
                    slug=slug
                )

        except ValueError:
            raise
        except Exception as e:
            loggers.error(f"Error getting tenant info for slug {slug}: {e}")
            raise HTTPException(status_code=500, detail=f"Database error: {e}")


# Global database manager instance
_db_manager: Optional[DatabaseConnectionManager] = None


def get_database_manager() -> DatabaseConnectionManager:
    """
    Get the global database manager instance.

    Returns:
        DatabaseConnectionManager instance
    """
    global _db_manager

    if _db_manager is None:
        # Load configuration from environment
        db_url = os.getenv("DB_URL")
        admin_db_name = os.getenv("ADMIN_DB_NAME")

        if not db_url or not admin_db_name:
            raise ValueError("DB_URL and ADMIN_DB_NAME environment variables are required")

        config = DatabaseConfig(
            connection_url=db_url,
            admin_db_name=admin_db_name
        )

        _db_manager = DatabaseConnectionManager(config)

    return _db_manager


# Backward compatibility functions (deprecated - use DatabaseConnectionManager directly)
async def get_admin_db():
    """
    DEPRECATED: Use get_database_manager().get_admin_db() instead.
    """
    manager = get_database_manager()
    async with manager.get_admin_db() as db:
        return db

async def get_db_from_tenant_id(tenant_id: str):
    """
    DEPRECATED: Use get_database_manager().get_tenant_db() instead.
    """
    manager = get_database_manager()
    return await manager.get_tenant_db(tenant_id)


async def get_tenant_id_and_name_from_slug(slug: str):
    """
    DEPRECATED: Use get_database_manager().get_tenant_info_by_slug() instead.
    """
    manager = get_database_manager()
    tenant_info = await manager.get_tenant_info_by_slug(slug)
    return {
        "_id": ObjectId(tenant_info.tenant_id),
        "name": tenant_info.name
    }

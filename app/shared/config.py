"""
Configuration settings for the application.
Loads environment variables and provides configuration constants.
"""

import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Database settings
DB_URL = os.getenv("DB_URL")
DATABASE_NAME = os.getenv("DATABASE_NAME")

# JWT settings
SECRET_KEY = os.getenv("SECRET_KEY")
ALGORITHM = "HS256"

# Redis settings
REDIS_HOST = os.getenv("REDIS_HOST", "localhost")
REDIS_PORT = int(os.getenv("REDIS_PORT", 6379))
REDIS_PASSWORD = None  # Disable Redis authentication for now
REDIS_DB = int(os.getenv("REDIS_DB", 0))

# Audio Queue Management settings
REDIS_QUEUE_MAX_CONCURRENT_SESSIONS = int(os.getenv("REDIS_QUEUE_MAX_CONCURRENT_SESSIONS", 50))
REDIS_QUEUE_SESSION_TIMEOUT = int(os.getenv("REDIS_QUEUE_SESSION_TIMEOUT", 300))
REDIS_QUEUE_CLEANUP_INTERVAL = int(os.getenv("REDIS_QUEUE_CLEANUP_INTERVAL", 60))
REDIS_QUEUE_HEALTH_CHECK_INTERVAL = int(os.getenv("REDIS_QUEUE_HEALTH_CHECK_INTERVAL", 30))

# Audio Processing settings
AUDIO_PROCESSING_TIMEOUT = int(os.getenv("AUDIO_PROCESSING_TIMEOUT", 120))
AUDIO_MAX_DURATION_SECONDS = int(os.getenv("AUDIO_MAX_DURATION_SECONDS", 300))
AUDIO_CHUNK_SIZE_BYTES = int(os.getenv("AUDIO_CHUNK_SIZE_BYTES", 4096))

# File Upload settings
MAX_AUDIO_FILE_SIZE = int(os.getenv("MAX_AUDIO_FILE_SIZE", 200 * 1024 * 1024))  # 200MB default
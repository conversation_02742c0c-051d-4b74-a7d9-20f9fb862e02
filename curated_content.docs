# Curated Content API Routes

Base URL: `/v1/management/curated`

## GET /themes

**Request:**
```
GET /v1/management/curated/themes?page=1&limit=50&search=culture&category=culture&is_active=true&start_date=2024-01-01&end_date=2024-12-31&sort_order=desc
Authorization: Bearer <jwt_token>
```

**cURL:**
```bash
curl -X 'GET' \
  'http://localhost:8204/v1/management/curated/themes?page=1&limit=50&search=culture&category=culture&is_active=true&start_date=2024-01-01&end_date=2024-12-31&sort_order=desc' \
  -H 'accept: application/json' \
  -H 'Authorization: Bearer <jwt_token>'
```

**Response:**
```json
{
  "data": [
    {
      "_id": "507f1f77bcf86cd799439011",
      "name": "नेपाली संस्कृति",
      "name_en": "Nepali Culture",
      "description": "नेपाली संस्कृति र परम्पराका बारेमा",
      "description_en": "About Nepali culture and traditions",
      "category": "culture",
      "icon": "🏛️",
      "color": "#FF6B6B",
      "is_active": true,
      "created_at": "2024-01-01T10:00:00Z",
      "updated_at": "2024-01-01T10:00:00Z"
    }
  ],
  "meta": {
    "page": 1,
    "limit": 50,
    "total": 25,
    "total_pages": 1
  }
}
```

## GET /themes/{theme_id}

**Request:**
```
GET /v1/management/curated/themes/507f1f77bcf86cd799439011?page=1&limit=20&difficulty_level=2&status=completed&gentype=primary
Authorization: Bearer <jwt_token>
```

**cURL:**
```bash
curl -X 'GET' \
  'http://localhost:8204/v1/management/curated/themes/685e2955f928ae494af5e978?page=1&limit=20' \
  -H 'accept: application/json' \
  -H 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbiIsInJvbGUiOiJhZ2VudCIsInRlbmFudF9pZCI6IjY4MzgzYzU2YjUzZGFmZTliOWU4NWZjNiIsImV4cCI6MTc1MTAxMzE0M30.YfJBsNJDFE5p15meBnDd0DD8SqshPKmT6DzRv6U1zgA'
```

**Response:**
```json
{
  "data": [
    {
      "_id": "507f1f77bcf86cd799439012",
      "theme_id": "507f1f77bcf86cd799439011",
      "title": "नेपाली त्योहारहरू",
      "title_en": "Nepali Festivals",
      "description": "नेपालका मुख्य त्योहारहरूका बारेमा प्रश्नहरू",
      "description_en": "Questions about major festivals of Nepal",
      "difficulty_level": 2,
      "status": "completed",
      "gentype": "primary",
      "task_item_ids": ["507f1f77bcf86cd799439013", "507f1f77bcf86cd799439014"],
      "total_items": 10,
      "created_at": "2024-01-01T10:00:00Z",
      "theme": {
        "id": "507f1f77bcf86cd799439011",
        "name": "नेपाली संस्कृति",
        "name_en": "Nepali Culture",
        "icon": "🏛️",
        "color": "#FF6B6B",
        "category": "culture"
      }
  
  }
  ],
  "meta": {
    "page": 1,
    "limit": 20,
    "total": 15,
    "total_pages": 1
  },
}
```




## GET /filtered

**Request:**
```
GET /v1/management/curated/filtered?page=1&limit=20&theme_id=507f1f77bcf86cd799439011&difficulty_level=2&status=completed&gentype=primary
Authorization: Bearer <jwt_token>
```

**cURL:**
```bash
curl -X 'GET' \
  'http://localhost:8204/v1/management/curated/filtered?page=1&limit=20&theme_id=507f1f77bcf86cd799439011&difficulty_level=2&status=completed&gentype=primary' \
  -H 'accept: application/json' \
  -H 'Authorization: Bearer <jwt_token>'
```

**Response:**
```json
{
  "data": [
    {
      "_id": "507f1f77bcf86cd799439012",
      "theme_id": "507f1f77bcf86cd799439011",
      "title": "नेपाली त्योहारहरू",
      "title_en": "Nepali Festivals",
      "description": "नेपालका मुख्य त्योहारहरूका बारेमा प्रश्नहरू",
      "description_en": "Questions about major festivals of Nepal",
      "difficulty_level": 2,
      "status": "completed",
      "gentype": "primary",
      "task_item_ids": ["507f1f77bcf86cd799439013"],
      "total_items": 10,
      "created_at": "2024-01-01T10:00:00Z",
      "theme": {
        "id": "507f1f77bcf86cd799439011",
        "name": "नेपाली संस्कृति",
        "name_en": "Nepali Culture",
        "icon": "🏛️",
        "color": "#FF6B6B",
        "category": "culture"
      }
    }
  ],
  "meta": {
    "page": 1,
    "limit": 20,
    "total": 45,
    "total_pages": 3
  }
}
```

## GET /theme/{theme_id}

**Request:**
```
GET /v1/management/curated/theme/507f1f77bcf86cd799439011
Authorization: Bearer <jwt_token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "_id": "507f1f77bcf86cd799439011",
    "name": "नेपाली संस्कृति",
    "name_en": "Nepali Culture",
    "description": "नेपाली संस्कृति र परम्पराका बारेमा",
    "description_en": "About Nepali culture and traditions",
    "category": "culture",
    "icon": "🏛️",
    "color": "#FF6B6B",
    "is_active": true,
    "created_at": "2024-01-01T10:00:00Z",
    "updated_at": "2024-01-01T10:00:00Z",
    "statistics": {
      "total_content_sets": 15,
      "total_content_items": 150,
      "average_items_per_set": 10.0
    }
  },
  "message": "Theme details retrieved successfully",
  "meta": {
    "timestamp": null,
    "request_id": null
  }
}
```




## POST /generate

**Request:**
```
POST /v1/management/curated/generate
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "content": "Generate questions about Nepali festivals"
}
```

**Response:**
```json
{
  "content": "Generate questions about Nepali festivals",
  "generated_by": "user_id_here",
  "status": "success",
  "message": "Content generated successfully"
}
```

## POST /get_prompts

**Request:**
```
POST /v1/management/curated/get_prompts
Authorization: Bearer <jwt_token>
```

**Response:**
```json
{
  "data": [
    {
      "_id": "507f1f77bcf86cd799439015",
      "content": "Generate questions about Nepali festivals",
      "user_id": "user_id_here",
      "task_set_id": "507f1f77bcf86cd799439016",
      "created_at": "2024-01-01T10:00:00Z",
      "status": "pending"
    }
  ]
}
```

## GET /filter/curated

**Request:**
```
GET /v1/management/curated/filter/curated
Authorization: Bearer <jwt_token>
```

**cURL:**
```bash
curl -X 'GET' \
  'http://localhost:8204/v1/management/curated/filter/curated' \
  -H 'accept: application/json' \
  -H 'Authorization: Bearer <jwt_token>'
```

**Response:**
```json
{
  "success": true,
  "data": {
    "themes": [
      {
        "id": "507f1f77bcf86cd799439011",
        "name": "नेपाली संस्कृति",
        "name_en": "Nepali Culture"
      },
      {
        "id": "507f1f77bcf86cd799439012",
        "name": "नेपाली भूगोल",
        "name_en": "Nepali Geography"
      }
    ],
    "status_values": ["pending", "completed", "in_progress"],
    "gentype_values": ["primary", "follow_up", "supplementary"],
    "difficulty_levels": [1, 2, 3],
    "sort_options": ["asc", "desc"],
    "date_filter_info": {
      "earliest_date": "2024-01-01",
      "latest_date": "2024-12-31",
      "format": "YYYY-MM-DD",
      "quick_filters": [
        {"label": "Last 7 days", "days": 7},
        {"label": "Last 30 days", "days": 30},
        {"label": "Last 90 days", "days": 90},
        {"label": "This year", "type": "year"}
      ]
    }
  },
  "message": "Filter options retrieved successfully",
  "meta": {
    "timestamp": null,
    "request_id": null
  }
}
```

## GET /filters/themes

**Request:**
```
GET /v1/management/curated/filters/themes
Authorization: Bearer <jwt_token>
```

**cURL:**
```bash
curl -X 'GET' \
  'http://localhost:8204/v1/management/curated/filters/themes' \
  -H 'accept: application/json' \
  -H 'Authorization: Bearer <jwt_token>'
```

**Response:**
```json
{
  "success": true,
  "data": {
    "categories": ["culture", "geography", "history", "language", "literature", "science"],
    "is_active_options": [true, false],
    "sort_options": ["asc", "desc"],
    "date_filter_info": {
      "earliest_date": "2024-01-01",
      "latest_date": "2024-12-31",
      "format": "YYYY-MM-DD",
      "quick_filters": [
        {"label": "Last 7 days", "days": 7},
        {"label": "Last 30 days", "days": 30},
        {"label": "Last 90 days", "days": 90},
        {"label": "This year", "type": "year"}
      ]
    }
  },
  "message": "Themes filter options retrieved successfully",
  "meta": {
    "timestamp": null,
    "request_id": null
  }
}
```

## POST /convert-to-task-set/{curated_content_set_id}

**Request:**
```
POST /v1/management/curated/convert-to-task-set/507f1f77bcf86cd799439011
Authorization: Bearer <jwt_token>
```

**cURL:**
```bash
curl -X 'POST' \
  'http://localhost:8204/v1/management/curated/convert-to-task-set/507f1f77bcf86cd799439011' \
  -H 'accept: application/json' \
  -H 'Authorization: Bearer <jwt_token>'
```

**Response (200 - New task set created):**
```json
{
  "success": true,
  "data": {
    "message": "Task set created successfully",
    "task_set_id": "507f1f77bcf86cd799439099",
    "status": "created"
  },
  "meta": {
    "timestamp": null,
    "request_id": null
  }
}
```

**Response (201 - Task set already exists):**
```json
{
  "success": true,
  "data": {
    "message": "Task set already exists for user",
    "task_set_id": "507f1f77bcf86cd799439088",
    "status": "exists"
  },
  "meta": {
    "timestamp": null,
    "request_id": null
  }
}
```

**Response (400 - Invalid ID):**
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid curated content set ID format",
    "details": {}
  },
  "meta": {
    "timestamp": null,
    "request_id": null
  }
}
```

**Response (404 - Not found):**
```json
{
  "success": false,
  "error": {
    "code": "NOT_FOUND",
    "message": "Curated content set not found",
    "details": {}
  },
  "meta": {
    "timestamp": null,
    "request_id": null
  }
}
```

## Filter Values

**Categories:** culture, geography, history, language, literature, science, sports, politics, economy, religion, art, music, dance, food, festivals, traditions, customs, wildlife, nature, tourism, education, technology, health, agriculture, business, entertainment

**Status:** pending, completed, in_progress, cancelled

**Gentype:** primary, follow_up, supplementary, review

**Difficulty:** 1 (easy), 2 (medium), 3 (hard)

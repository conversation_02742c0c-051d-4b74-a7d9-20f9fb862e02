pipeline {
    agent any

    environment {
        APP_DIR = "${env.WORKSPACE}"
    }

    stages {
        stage('Set Up .env File') {
            steps {
                dir("${APP_DIR}") {
                    withCredentials([file(credentialsId: 'nep_app_env', variable: 'ENV_FILE')]) {
                        sh '''
                            echo "📄 Copying .env file..."
                            cp "$ENV_FILE" .env
                            chmod 600 .env
                        '''
                    }
                }
            }
        }

        stage('Docker Build') {
            steps {
                dir("${APP_DIR}") {
                    script {
                        try {
                            echo "🏗 Building Docker images..."
                            sh 'docker compose --env-file .env build --no-cache'
                            env.BUILD_SUCCEEDED = "true"
                        } catch (Exception err) {
                            echo "❌ Build failed: ${err}"
                            currentBuild.result = 'FAILURE'
                            env.BUILD_SUCCEEDED = "false"
                        }
                    }
                }
            }
        }

        stage('Rolling Restart Deploy') {
            when {
                expression { env.BUILD_SUCCEEDED == "true" }
            }
            steps {
                dir("${APP_DIR}") {
                    script {
                        try {
                            echo "🚀 Rolling restart with new images..."
                            sh 'docker compose --env-file .env up -d --force-recreate --no-deps'
                            
                            echo "✅ Deployment successful"
                        } catch (Exception err) {
                            echo "❌ Deploy failed: ${err}"
                            currentBuild.result = 'FAILURE'
                            throw err
                        }
                    }
                }
            }
        }
    }

    post {
        success {
            echo '✅ Done!'
        }
        failure {
            echo '❌ Failed!'
        }
        always {
            echo "📌 Finished."
        }
    }
}